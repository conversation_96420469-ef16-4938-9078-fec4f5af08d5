"use client";

import { useQuery } from "@tanstack/react-query";
import { PropertyGridSkeleton } from "@/components/loading-states";
import { PageLayout } from "@/components/page-layout";
import { NewProperty } from "@/components/properties/new-property";
import { PropertyCard } from "@/components/properties/property-card";
import { QueryRenderer } from "@/components/query-renderer";
import { useTRPC } from "@/components/trpc/client";

export default function PropertiesPage() {
  const trpc = useTRPC();
  const { data: properties, isLoading } = useQuery(
    trpc.properties.list.queryOptions()
  );

  return (
    <PageLayout title="Properties">
      <div className="flex h-full gap-4">
        <QueryRenderer
          data={properties}
          isLoading={isLoading}
          loadingComponent={<PropertyGridSkeleton />}
          emptyComponent={
            <div className="text-center">
              <p className="text-muted-foreground">
                No properties found. Create your first property to get started.
              </p>
            </div>
          }
        >
          {(properties) => (
            <>
              {properties.map((property) => (
                <PropertyCard key={property.id} property={property} />
              ))}
            </>
          )}
        </QueryRenderer>

        <div className="flex h-full items-center justify-center">
          <NewProperty />
        </div>
      </div>
    </PageLayout>
  );
}

"use client";

import { useQuery } from "@tanstack/react-query";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  Briefcase,
  CheckCircle,
  Clock,
  Users,
} from "lucide-react";
import Link from "next/link";
import {
  <PERSON>,
  <PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import { Header } from "@/components/header";
import { useTRPC } from "@/components/trpc/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";

export default function Dashboard() {
  const trpc = useTRPC();

  // Fetch summary data
  const { data: stats } = useQuery(trpc.admin.getStats.queryOptions());
  const { data: recentJobs } = useQuery(
    trpc.jobs.list.queryOptions({ limit: 5 }),
  );

  return (
    <>
      <Header title="Admin Dashboard" />
      <div className="space-y-8 p-8">
        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            title="Total Users"
            value={stats?.totalUsers || 0}
            description="Total registered users"
            trend={stats?.usersTrend || 0}
            icon={<Users className="h-5 w-5 text-blue-500" />}
          />
          <StatsCard
            title="Active Jobs"
            value={stats?.activeJobs || 0}
            description="Currently published jobs"
            trend={stats?.jobsTrend || 0}
            icon={<Briefcase className="h-5 w-5 text-orange-500" />}
          />
          <StatsCard
            title="Completed Jobs"
            value={stats?.completedJobs || 0}
            description="Successfully completed jobs"
            trend={stats?.completedJobsTrend || 0}
            icon={<CheckCircle className="h-5 w-5 text-green-500" />}
          />
          <StatsCard
            title="Total Revenue"
            value={formatCurrency(stats?.totalRevenue || 0)}
            description="Platform revenue"
            trend={stats?.revenueTrend || 0}
            icon={<Clock className="h-5 w-5 text-purple-500" />}
          />
        </div>

        {/* Charts */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Jobs by Status</CardTitle>
              <CardDescription>
                Distribution of jobs by current status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats?.jobsByStatus || []}>
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#f97316" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Monthly Revenue</CardTitle>
              <CardDescription>
                Revenue trends over the past 6 months
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats?.revenueByMonth || []}>
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => formatCurrency(value as number)}
                    />
                    <Bar dataKey="value" fill="#22c55e" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Recent Jobs</CardTitle>
              <CardDescription>
                Latest jobs created on the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentJobs?.map((job) => (
                  <div
                    key={job.id}
                    className="flex items-center justify-between border-b pb-2"
                  >
                    <div>
                      <Link
                        href={`/admin/jobs/${job.id}`}
                        className="font-medium hover:text-orange-600"
                      >
                        {job.name}
                      </Link>
                      <p className="text-muted-foreground text-sm">
                        Budget: {formatCurrency(job.budget)}
                      </p>
                    </div>
                    <div className="text-muted-foreground text-sm">
                      {job.bidsCount} bids
                    </div>
                  </div>
                ))}
                <div className="pt-2 text-center">
                  <Link
                    href="/admin/jobs"
                    className="text-orange-600 text-sm hover:text-orange-700"
                  >
                    View all jobs →
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Users</CardTitle>
              <CardDescription>
                Latest users registered on the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="pt-2 text-center">
                  <Link
                    href="/admin/users"
                    className="text-orange-600 text-sm hover:text-orange-700"
                  >
                    View all users →
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}

function StatsCard({
  title,
  value,
  description,
  trend,
  icon,
}: {
  title: string;
  value: string | number;
  description: string;
  trend: number;
  icon: React.ReactNode;
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="font-medium text-sm">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="font-bold text-2xl">{value}</div>
        <p className="text-muted-foreground text-xs">{description}</p>
        {trend !== 0 && (
          <div className="mt-1 flex items-center">
            {trend > 0 ? (
              <ArrowUpIcon className="mr-1 h-3 w-3 text-green-500" />
            ) : (
              <ArrowDownIcon className="mr-1 h-3 w-3 text-red-500" />
            )}
            <span
              className={`text-xs ${
                trend > 0 ? "text-green-500" : "text-red-500"
              }`}
            >
              {Math.abs(trend)}% from last month
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

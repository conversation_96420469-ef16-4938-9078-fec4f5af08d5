"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Blog from "@/components/marketing/blog";
import Features from "@/components/marketing/features";
import Footer from "@/components/marketing/footer";
import Hero from "@/components/marketing/hero";
import People from "@/components/marketing/people";
import { useSession } from "@/lib/auth-client";

export default function HomePage() {
  const router = useRouter();
  const { data: session } = useSession();

  // JSON-LD structured data for better SEO
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "TradeCrews",
    url: "https://tradecrews.com",
    logo: "https://tradecrews.com/logo.png",
    description:
      "Connecting homeowners with trusted trade professionals for home improvement and construction projects.",
    sameAs: [
      "https://twitter.com/tradecrews",
      "https://facebook.com/tradecrews",
      "https://linkedin.com/company/tradecrews",
    ],
  };

  useEffect(() => {
    if (session) {
      router.push("/dashboard");
      return;
    }
  });

  return (
    <main>
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: JSON-LD structured data
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <Hero />
      <Features />
      <Blog />
      <People />
      <Footer />
    </main>
  );
}

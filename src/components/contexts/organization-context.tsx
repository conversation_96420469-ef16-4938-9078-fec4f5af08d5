"use client";

import { useQuery } from "@tanstack/react-query";
import {
  createContext,
  Suspense,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTRPC } from "@/components/trpc/client";
import type { Organization } from "@/db/schema";

export type OrganizationContextType = {
  organization: Organization | null | undefined;
  setOrganization: (organization: Organization | null | undefined) => void;
  organizationList: Organization[] | null | undefined;
  setOrganizationList: (
    organizationList: Organization[] | null | undefined,
  ) => void;
};

const OrganizationContext = createContext<OrganizationContextType>({
  organization: null,
  setOrganization: () => {},
  organizationList: null,
  setOrganizationList: () => {},
});

function OrganizationLoader({
  setOrganization,
}: {
  setOrganization: (org: Organization) => void;
}) {
  const trpc = useTRPC();
  const { data: userOrganization } = useQuery(
    trpc.contractor.getForUser.queryOptions(),
  );

  useEffect(() => {
    if (userOrganization) {
      setOrganization(userOrganization);
    }
  }, [userOrganization, setOrganization]);

  return null;
}

export function OrganizationProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [organization, setOrganization] = useState<
    Organization | null | undefined
  >(null);

  const value = {
    organization,
    setOrganization,
    // We keep these properties for backward compatibility
    // but they're no longer needed for multiple organizations
    organizationList: organization ? [organization] : [],
    setOrganizationList: () => {}, // No-op function
  };

  return (
    <OrganizationContext.Provider value={value}>
      <Suspense fallback={null}>
        <OrganizationLoader setOrganization={setOrganization} />
      </Suspense>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  return useContext(OrganizationContext);
}

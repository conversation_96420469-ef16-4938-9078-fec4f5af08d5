"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FaMicrosoft } from "react-icons/fa";
import { FcGoogle } from "react-icons/fc";
import { toast } from "sonner";
import { z } from "zod/v4";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { signIn } from "@/lib/auth-client";

const formSchema = z.object({
  email: z.email(),
  password: z.string().min(8),
});

type FormSchema = z.infer<typeof formSchema>;

export function SignInForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState<string | null>(null);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: FormSchema) {
    setIsLoading(true);
    try {
      await signIn.email(
        {
          email: values.email,
          password: values.password,
        },
        {
          onSuccess: () => {
            router.push("/dashboard");
            toast.success("Signed in successfully");
          },
          onError: (ctx) => {
            toast.error(ctx.error.message);
          },
        },
      );
    } catch (error) {
      toast.error("An error occurred during sign in");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleSocialSignIn = async (provider: "google" | "microsoft") => {
    setSocialLoading(provider);
    try {
      await signIn.social(
        {
          provider,
        },
        {
          onSuccess: () => {
            router.push("/dashboard");
            toast.success("Signed in successfully");
          },
          onError: (ctx) => {
            toast.error(ctx.error.message);
          },
        },
      );
    } catch (error) {
      toast.error(`An error occurred during ${provider} sign in`);
      console.error(error);
    } finally {
      setSocialLoading(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => handleSocialSignIn("google")}
          disabled={socialLoading !== null}
          className="w-full"
        >
          <FcGoogle className="mr-2 h-4 w-4" />
          {socialLoading === "google"
            ? "Signing in..."
            : "Continue with Google"}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={() => handleSocialSignIn("microsoft")}
          disabled={socialLoading !== null}
          className="w-full"
        >
          <FaMicrosoft className="mr-2 h-4 w-4 text-microsoft-blue" />
          {socialLoading === "microsoft"
            ? "Signing in..."
            : "Continue with Microsoft"}
        </Button>
      </div>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || socialLoading !== null}
          >
            {isLoading ? "Signing in..." : "Sign in with Email"}
          </Button>
        </form>
      </Form>
    </div>
  );
}

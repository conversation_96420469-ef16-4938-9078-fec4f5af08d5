"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { PlusIcon, TrashIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { type SubmitH<PERSON><PERSON>, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod/v4";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import type { JobTemplate } from "@/db/schema";

const templateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Description is required"),
  budget: z.number().min(1, "Budget must be greater than 0"),
  estimatedDuration: z.number().min(1, "Duration must be at least 1 day"),
  tasks: z
    .array(
      z.object({
        name: z.string().min(1, "Task name is required"),
        tradeId: z.string().min(1, "Trade is required"),
      }),
    )
    .min(1, "At least one task is required"),
});

type FormSchema = z.infer<typeof templateSchema>;

interface TemplateFormProps {
  initialData?: JobTemplate;
}

export function TemplateForm({ initialData }: TemplateFormProps) {
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(templateSchema),
    defaultValues: initialData
      ? { ...initialData }
      : {
          name: "",
          description: "",
          budget: 0,
          estimatedDuration: 7,
          tasks: [],
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "tasks",
  });

  const trpc = useTRPC();
  const createTemplate = useMutation(trpc.templates.create.mutationOptions());
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();

    createTemplate.mutate(
      { ...data },
      {
        onSuccess: () => {
          toast.success("Template created successfully");
          router.push("/admin/templates");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating template:", error);
          toast.error("Failed to create template");
        },
      },
    );
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Kitchen Renovation" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="budget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Estimated Budget ($)</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="100" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe what this project template includes..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="estimatedDuration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Estimated Duration (days)</FormLabel>
                <FormControl>
                  <Input type="number" min="1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Tasks</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => append({ name: "", tradeId: "" })}
              >
                <PlusIcon className="mr-2 h-4 w-4" />
                Add Task
              </Button>
            </div>

            {fields.map((field, index) => (
              <div
                key={field.id}
                className="grid grid-cols-1 gap-4 rounded-lg border p-4 md:grid-cols-[1fr,1fr,auto]"
              >
                <FormField
                  control={form.control}
                  name={`tasks.${index}.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Task Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Install Cabinets" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`tasks.${index}.tradeId`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Trade</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a trade" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {trades?.map((trade) => (
                            <SelectItem key={trade.id} value={trade.id}>
                              {trade.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-end">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => remove(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 flex justify-end gap-4">
            <Button
              type="button"
              onClick={() => router.back()}
              variant="outline"
            >
              Cancel
            </Button>
            <Button type="submit" className="bg-orange-600 hover:bg-orange-700">
              Create Template
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

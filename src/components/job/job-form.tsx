"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  BriefcaseIcon,
  CalendarIcon,
  DollarSignIcon,
  ListTodoIcon,
  PlusIcon,
  TrashIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { type SubmitHandler, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod/v4";
import { useJob } from "@/components/contexts/job-context";
import { TemplateSelector } from "@/components/job/template-selector";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { Job, JobTemplate } from "@/db/schema";
import { cn } from "@/lib/utils";
import { useTRPC } from "../trpc/client";
import { Calendar } from "../ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Separator } from "../ui/separator";
import { JobImageUploader } from "./job-image-uploader";

interface JobFormProps {
  initialData?: Job;
  propertyId?: string;
  jobType?: "STANDARD" | "QUICK_HIRE";
  inWizard?: boolean;
  onJobCreated?: (id: string, tradeId?: string) => void;
}

const jobsSchema = z
  .object({
    name: z.string().min(1, "Name is required"),
    startsAt: z.date().refine((date) => date > new Date(), {
      message: "Start date must be in the future",
    }),
    deadline: z.date().refine((date) => date > new Date(), {
      message: "Bid deadline must be in the future",
    }),
    budget: z.number(),
    propertyId: z.string().min(1, "Property is required"),
    tasks: z.object({ name: z.string(), tradeId: z.string() }).array(),
    images: z
      .object({
        url: z.string(),
        description: z.string().optional().nullable(),
      })
      .array()
      .optional(),
    jobType: z.enum(["STANDARD", "QUICK_HIRE"]),
    isRecurring: z.boolean(),
    recurringFrequency: z.string().nullable(),
    contractorId: z.string().optional(),
    serviceId: z.string().optional(),
  })
  .refine((data) => data.deadline < data.startsAt, {
    message: "Bid deadline must be before the start date",
    path: ["deadline"], // This shows the error on the deadline field
  });

type FormSchema = z.infer<typeof jobsSchema>;

export function JobForm({
  initialData,
  propertyId,
  jobType = "STANDARD",
  inWizard = false,
  onJobCreated,
}: JobFormProps) {
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(jobsSchema),
    defaultValues: initialData
      ? {
          ...initialData,
        }
      : {
          name: "",
          tasks: [],
          budget: 0,
          propertyId: propertyId || "",
          startsAt: undefined,
          deadline: undefined,
          images: [],
          jobType: jobType, // Use the prop value
          isRecurring: false,
          recurringFrequency: null,
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "tasks",
  });

  const trpc = useTRPC();
  const createJob = useMutation(trpc.jobs.create.mutationOptions());
  const updateJob = useMutation(trpc.jobs.update.mutationOptions());
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());
  const { data: properties } = useQuery(trpc.properties.list.queryOptions());
  const { data: quickHireTrades } = useQuery(
    trpc.trades.listAvailableForQuickHire.queryOptions(),
  );

  const mutation = initialData ? updateJob : createJob;

  const { jobList, setJobList } = useJob();

  const applyTemplate = (template: JobTemplate) => {
    // Set form values from template
    form.setValue("name", template.name);
    form.setValue("budget", template.budget);

    // Clear existing tasks and add template tasks
    const tasks = template.tasks.map((task) => ({
      name: task.name,
      tradeId: task.tradeId,
    }));

    form.setValue("tasks", tasks);
  };

  const handleAddTask = () => {
    // If it's a quick hire job and already has a task, don't allow adding more
    if (form.watch("jobType") === "QUICK_HIRE" && fields.length >= 1) {
      toast.error("Quick hire jobs can only have one task");
      return;
    }

    append({ name: "", tradeId: "" });
  };

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();

    // Validate that images have valid URLs
    const validImages = data.images?.filter((img) =>
      img.url?.startsWith("http"),
    );

    if (data.images && data.images.length > 0 && validImages?.length === 0) {
      toast.error(
        "Image uploads failed. Please try again or continue without images.",
      );
    }

    const id = initialData?.id || "";

    // Use the valid images
    const submitData = {
      ...data,
      images: validImages,
    };

    // For quick hire outside wizard, validate contractor and service selection
    if (!inWizard && data.jobType === "QUICK_HIRE") {
      if (!data.contractorId) {
        toast.error("Please select a contractor for quick hire");
        return;
      }

      if (!data.serviceId) {
        toast.error("Please select a service for quick hire");
        return;
      }
    }

    mutation.mutate(
      { id, ...submitData },
      {
        onSuccess: (data) => {
          toast.success(`Project ${id ? "updated" : "created"} successfully!`);

          // If we're in the wizard, call the callback
          if (inWizard && onJobCreated) {
            // For quick hire, pass the trade ID of the first task
            const tradeId =
              form.getValues("jobType") === "QUICK_HIRE" &&
              form.getValues("tasks").length > 0
                ? form.getValues("tasks")[0]?.tradeId
                : undefined;

            onJobCreated(data.id, tradeId);
          } else {
            // Original behavior
            jobList?.push(data);
            setJobList(jobList);
            router.push("/dashboard");
            router.refresh();
          }
        },
        onError: (error) => {
          console.error("Error creating job:", error);
          toast.error("Failed to save project. Please try again.");
        },
      },
    );
  };

  // Add these queries for contractors and services
  const { data: contractors } = useQuery(
    trpc.contractor.listByTrade.queryOptions(
      {
        tradeId: form.watch("tasks")[0]?.tradeId || "",
        acceptsQuickHire: form.watch("jobType") === "QUICK_HIRE",
      },
      {
        enabled:
          form.watch("jobType") === "QUICK_HIRE" &&
          form.watch("tasks").length > 0 &&
          !!form.watch("tasks")[0]?.tradeId,
      },
    ),
  );

  const { data: services } = useQuery(
    trpc.contractor.getServices.queryOptions(
      {
        organizationId: form.watch("contractorId") || "",
      },
      {
        enabled:
          !!form.watch("contractorId") &&
          form.watch("jobType") === "QUICK_HIRE",
      },
    ),
  );

  // Reset contractor and service when trade changes
  useEffect(() => {
    if (form.watch("jobType") === "QUICK_HIRE") {
      form.setValue("contractorId", "");
      form.setValue("serviceId", "");
    }
  }, [form.watch, form.setValue]);

  // Reset service when contractor changes
  useEffect(() => {
    form.setValue("serviceId", "");
  }, [form.setValue]);

  return (
    <div
      className={`flex h-full flex-col ${!inWizard ? "overflow-y-scroll" : ""}`}
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="mx-auto max-w-2/3 space-y-8"
        >
          <div className="mb-6">
            <div className="mb-4 flex items-center gap-2">
              <BriefcaseIcon className="h-5 w-5 text-orange-500" />
              <h3 className="font-medium text-lg">Project Details</h3>
            </div>
            <Separator className="mb-6" />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="mb-4">
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Kitchen Remodel"
                      {...field}
                      className="focus-visible:ring-orange-500"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {!propertyId && (
              <FormField
                control={form.control}
                name="propertyId"
                render={({ field }) => (
                  // biome-ignore lint/nursery/useUniqueElementIds: Used for tour
                  <FormItem id="propertyId">
                    <FormLabel>Property</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="focus-visible:ring-orange-500">
                          <SelectValue placeholder="Select a property" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {properties?.map((property) => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <div className="mb-4 flex justify-end">
              <TemplateSelector onSelectTemplate={applyTemplate} />
            </div>
          </div>

          {/** biome-ignore lint/nursery/useUniqueElementIds: Used for tour */}
          <div className="mb-6" id="tasks">
            <div className="mb-4 flex items-center gap-2">
              <ListTodoIcon className="h-5 w-5 text-orange-500" />
              <h3 className="font-medium text-lg">Tasks</h3>
              <div className="flex-1" />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddTask}
                disabled={
                  form.watch("jobType") === "QUICK_HIRE" && fields.length >= 1
                }
                className="flex items-center gap-1"
              >
                <PlusIcon className="h-4 w-4" />
                Add Task
              </Button>
            </div>
            <Separator className="mb-6" />

            {fields.length === 0 && (
              <div className="rounded-lg border border-muted-foreground/20 border-dashed p-8 text-center">
                <p className="text-muted-foreground text-sm">
                  No tasks added yet.
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddTask}
                  className="mt-4 flex items-center gap-1"
                >
                  <PlusIcon className="h-4 w-4" />
                  Add Your First Task
                </Button>
              </div>
            )}

            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className="space-y-4 rounded-md border p-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Task {index + 1}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => remove(index)}
                      className="h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-600"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name={`tasks.${index}.name`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Task Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Install Cabinets"
                              {...field}
                              className="focus-visible:ring-orange-500"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`tasks.${index}.tradeId`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Trade</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a trade" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {/* Filter trades based on job type */}
                              {(form.watch("jobType") === "STANDARD"
                                ? trades
                                : quickHireTrades
                              )?.map((trade) => (
                                <SelectItem key={trade.id} value={trade.id}>
                                  {trade.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <div className="mb-4 flex items-center gap-2">
              <DollarSignIcon className="h-5 w-5 text-orange-500" />
              <h3 className="font-medium text-lg">Budget</h3>
            </div>
            <Separator className="mb-6" />

            <FormField
              control={form.control}
              name="budget"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Budget Amount</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute top-1.5 left-3 text-muted-foreground">
                        $
                      </span>
                      <Input
                        type="number"
                        placeholder="1000"
                        {...field}
                        onChange={(e) =>
                          field.onChange(Number.parseInt(e.target.value) || 0)
                        }
                        className="pl-7 focus-visible:ring-orange-500"
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mb-6">
            <div className="mb-4 flex items-center gap-2">
              <CalendarIcon className="h-5 w-5 text-orange-500" />
              <h3 className="font-medium text-lg">Timeline</h3>
            </div>
            <Separator className="mb-6" />

            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="startsAt"
                render={({ field }) => (
                  // biome-ignore lint/nursery/useUniqueElementIds: Used for tour
                  <FormItem className="flex flex-col" id="startsAt">
                    <FormLabel>Start Date</FormLabel>
                    <Popover modal={true}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal focus-visible:ring-orange-500",
                              !field.value && "text-muted-foreground",
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto size-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0"
                        align="start"
                        side="bottom"
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="deadline"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Bid Deadline</FormLabel>
                    <FormDescription>
                      The date by which contractors must submit their bids
                    </FormDescription>
                    <Popover modal={true}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal focus-visible:ring-orange-500",
                              !field.value && "text-muted-foreground",
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto size-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0"
                        align="start"
                        side="bottom"
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="mb-6">
            <FormField
              control={form.control}
              name="images"
              render={({ field }) => (
                <FormItem>
                  <JobImageUploader
                    initialImages={field.value}
                    onChange={field.onChange}
                    maxImages={10}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className={cn("space-y-4", inWizard && "hidden")}>
            <FormField
              control={form.control}
              name="jobType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Type</FormLabel>
                  <FormDescription>
                    Choose how you want to hire for this project
                  </FormDescription>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="grid grid-cols-2 gap-4"
                  >
                    <FormItem className="flex flex-col items-center space-y-2 rounded-md border p-4">
                      <FormControl>
                        <RadioGroupItem value="STANDARD" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Standard Bidding
                      </FormLabel>
                      <p className="text-center text-muted-foreground text-xs">
                        Collect multiple bids from professionals
                      </p>
                    </FormItem>

                    {/* Only show Quick Hire option if there are eligible trades */}
                    {quickHireTrades && quickHireTrades.length > 0 && (
                      <FormItem className="flex flex-col items-center space-y-2 rounded-md border p-4">
                        <FormControl>
                          <RadioGroupItem value="QUICK_HIRE" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Quick Hire
                        </FormLabel>
                        <p className="text-center text-muted-foreground text-xs">
                          Directly hire a professional for small or recurring
                          jobs
                        </p>
                      </FormItem>
                    )}
                  </RadioGroup>
                </FormItem>
              )}
            />

            {form.watch("jobType") === "QUICK_HIRE" && (
              <>
                <FormField
                  control={form.control}
                  name="isRecurring"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Recurring Service</FormLabel>
                        <FormDescription>
                          Is this a service that needs to be performed
                          regularly?
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {form.watch("isRecurring") && (
                  <FormField
                    control={form.control}
                    name="recurringFrequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Frequency</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value as string}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="biweekly">Bi-weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </>
            )}
          </div>

          {!inWizard &&
            form.watch("jobType") === "QUICK_HIRE" &&
            form.watch("tasks").length > 0 && (
              <div className="mb-6">
                <div className="mb-4 flex items-center gap-2">
                  <BriefcaseIcon className="h-5 w-5 text-orange-500" />
                  <h3 className="font-medium text-lg">Quick Hire Details</h3>
                </div>
                <Separator className="mb-6" />

                <FormField
                  control={form.control}
                  name="contractorId"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>Select Contractor</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="focus-visible:ring-orange-500">
                            <SelectValue placeholder="Select a contractor" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {contractors?.map((contractor) => (
                            <SelectItem
                              key={contractor.id}
                              value={contractor.id}
                            >
                              {contractor.name}
                            </SelectItem>
                          ))}
                          {contractors?.length === 0 && (
                            <SelectItem value="none" disabled>
                              No contractors available for this trade
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch("contractorId") && (
                  <FormField
                    control={form.control}
                    name="serviceId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Select Service</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="focus-visible:ring-orange-500">
                              <SelectValue placeholder="Select a service" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {services?.map((service) => (
                              <SelectItem key={service.id} value={service.id}>
                                {service.name} - ${service.price}
                              </SelectItem>
                            ))}
                            {services?.length === 0 && (
                              <SelectItem value="none" disabled>
                                No services available from this contractor
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}

          {!inWizard ? (
            <div className="mt-8 flex justify-end gap-4 pb-4">
              <Button
                type="button"
                onClick={() => router.back()}
                variant="outline"
              >
                Cancel
              </Button>
              <Button type="submit" variant="tc_orange">
                {initialData ? "Update" : "Create"} Project
              </Button>
            </div>
          ) : (
            <div className="mt-8 flex justify-end gap-4 pb-4">
              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  window.dispatchEvent(new CustomEvent("job-wizard-back"))
                }
              >
                Back
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  window.dispatchEvent(new CustomEvent("job-wizard-cancel"))
                }
              >
                Cancel
              </Button>
              <Button type="submit" variant="tc_orange">
                {jobType === "STANDARD"
                  ? "Create Project"
                  : "Continue to Select Professional"}
              </Button>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}

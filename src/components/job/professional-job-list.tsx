"use client";

import { useQuery } from "@tanstack/react-query";
import { Search } from "lucide-react";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useState } from "react";
import { useOrganization } from "@/components/contexts/organization-context";
import type { JobsMapProps } from "@/components/job/jobs-map";
import { useTRPC } from "@/components/trpc/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Slider } from "@/components/ui/slider";
import type { Organization } from "@/db/schema";

export function ProfessionalJobList() {
  const trpc = useTRPC();
  const [searchQuery, setSearchQuery] = useState("");
  const [maxDistance, setMaxDistance] = useState(50);
  const { organization } = useOrganization();
  const organizationId = organization?.id;

  // Fetch jobs with distance information
  const { data: jobsData, isLoading } = useQuery(
    trpc.jobs.listPublishedByDistance.queryOptions(
      {
        maxDistance,
      },
      {
        enabled: !!organizationId,
      },
    ),
  );

  // Filter jobs based on search query
  const filteredJobs = jobsData?.filter((job) =>
    job.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="p-8">
      <div className="mb-4 flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search jobs..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">
            Distance: {maxDistance} miles
          </span>
          <Slider
            className="w-40"
            value={[maxDistance]}
            min={5}
            max={100}
            step={5}
            onValueChange={(value) => setMaxDistance(value[0] as number)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Available Jobs</CardTitle>
            <CardDescription>
              {filteredJobs?.length || 0} jobs within {maxDistance} miles
            </CardDescription>
          </CardHeader>
          <CardContent className="max-h-[600px] overflow-y-auto">
            {isLoading ? (
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Skeleton
                    key={`skeleton-${
                      // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
                      i
                    }`}
                    className="h-20"
                  />
                ))}
              </div>
            ) : (
              <div className="divide-y">
                {filteredJobs?.map((job) => (
                  <div key={job.id} className="py-3">
                    <Link
                      href={`/jobs/${job.id}`}
                      className="block rounded-md bg-muted p-2 transition-colors hover:bg-muted/50"
                    >
                      <div className="font-medium">{job.name}</div>
                      <div className="text-muted-foreground text-sm">
                        {job.property?.name || "Property"}
                      </div>
                      <div className="mt-1 flex items-center gap-2">
                        <span className="text-sm">${job.budget}</span>
                        {job.distance && (
                          <span className="text-muted-foreground text-xs">
                            {Number(job.distance).toFixed(1)} miles away
                          </span>
                        )}
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Job Locations</CardTitle>
          </CardHeader>
          <CardContent>
            <JobsMap
              jobs={
                filteredJobs?.filter(
                  (job) =>
                    job.property?.address?.location &&
                    typeof job.property.address.location === "object",
                ) || []
              }
              organization={organization as Organization}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function JobsMap({ jobs, organization }: JobsMapProps) {
  const JobsMap = dynamic(() => import("@/components/job/jobs-map"), {
    loading: () => <Skeleton className="h-[60vh]" />,
    ssr: false,
  });

  return (
    <div>
      <JobsMap jobs={jobs} organization={organization} />
    </div>
  );
}

"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { type SubmitHand<PERSON>, useForm } from "react-hook-form";
import { z } from "zod/v4";
import { useOrganization } from "@/components/contexts/organization-context";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import type { Organization } from "@/db/schema";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  trade: z.object({
    id: z.string().min(1, "Please select a trade"),
  }),
  description: z.string().optional(),
  email: z.email().optional(),
  phone: z.string().optional(),
  address: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zip: z.string().min(1, "ZIP code is required"),
  }),
  acceptsQuickHire: z.boolean().default(false),
});

type FormSchema = z.infer<typeof formSchema>;

interface ContractorFormProps {
  initialData?: Organization;
  onboarding?: boolean;
  onSuccess?: () => void;
}

export function ContractorForm({
  initialData,
  onboarding,
  onSuccess,
}: ContractorFormProps) {
  const router = useRouter();
  const trpc = useTRPC();

  const createOrganization = useMutation(
    trpc.contractor.create.mutationOptions(),
  );

  const updateOrganization = useMutation(
    trpc.contractor.update.mutationOptions(),
  );

  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  const mutation = initialData ? updateOrganization : createOrganization;

  const { setOrganization } = useOrganization();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || "",
      trade: {
        id: initialData?.trade?.id || "",
      },
      description: initialData?.description || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      address: {
        street: initialData?.address?.street || "",
        city: initialData?.address?.city || "",
        state: initialData?.address?.state || "",
        zip: initialData?.address?.zip || "",
      },
      acceptsQuickHire: initialData?.acceptsQuickHire || false,
    },
  });

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();

    const id = initialData?.id || "";

    mutation.mutate(
      { id, ...data },
      {
        onSuccess: async (data) => {
          setOrganization(data);

          if (onboarding && onSuccess) {
            onSuccess();
          }

          router.push("/dashboard");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating organization:", error);
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="max-w-md space-y-8"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="ACME Plumbing" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="trade.id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Trade</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your trade..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {trades?.map((trade) => (
                    <SelectItem value={trade.id} key={trade.id}>
                      {trade.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email (Optional)</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="(*************" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="mb-6">
          <h3 className="mb-4 font-medium text-lg">Address</h3>

          <FormField
            control={form.control}
            name="address.street"
            render={({ field }) => (
              <FormItem className="mb-4">
                <FormLabel>Street</FormLabel>
                <FormControl>
                  <Input placeholder="123 Main St" {...field} />
                </FormControl>
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="address.city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input placeholder="San Francisco" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address.state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <FormControl>
                    <Input placeholder="CA" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="address.zip"
            render={({ field }) => (
              <FormItem className="mt-4">
                <FormLabel>ZIP Code</FormLabel>
                <FormControl>
                  <Input placeholder="94103" {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <div className="mb-6">
          <FormField
            control={form.control}
            name="acceptsQuickHire"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Accept Quick Hire Jobs</FormLabel>
                  <p className="text-muted-foreground text-sm">
                    Allow homeowners to hire you directly for small or recurring
                    jobs without bidding
                  </p>
                </div>
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-4">
          <Button type="submit">
            {initialData ? "Update" : "Create"} Organization
          </Button>
          <Button
            type="button"
            onClick={() => router.back()}
            variant="secondary"
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
